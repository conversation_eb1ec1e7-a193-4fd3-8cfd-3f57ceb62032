package vn.vinclub.partner.service.impl;

import com.fasterxml.jackson.databind.JsonNode;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import vn.vinclub.common.util.BaseJsonUtils;
import vn.vinclub.partner.constant.AppErrorCode;
import vn.vinclub.partner.domain.dto.module.PartnerModuleCreateDto;
import vn.vinclub.partner.domain.dto.module.PartnerModuleFilterDto;
import vn.vinclub.partner.domain.dto.module.PartnerModuleUpdateDto;
import vn.vinclub.partner.domain.dto.module.config.*;
import vn.vinclub.partner.domain.entity.Partner;
import vn.vinclub.partner.domain.entity.PartnerModule;
import vn.vinclub.partner.domain.entity.PartnerPointConfig;
import vn.vinclub.partner.domain.enums.ConversionType;
import vn.vinclub.partner.domain.enums.IntegrationTag;
import vn.vinclub.partner.domain.enums.ModuleType;
import vn.vinclub.partner.domain.enums.PartnerStatus;
import vn.vinclub.partner.domain.mapper.PartnerModuleMapper;
import vn.vinclub.partner.exception.BusinessLogicException;
import vn.vinclub.partner.redis.RedisPublish;
import vn.vinclub.partner.repository.PartnerModuleRepository;
import vn.vinclub.partner.service.PartnerPointConfigService;
import vn.vinclub.partner.service.PartnerService;

import java.math.BigDecimal;
import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class PartnerModuleServiceImplTest {

    @Mock
    private PartnerModuleRepository partnerModuleRepository;

    @Mock
    private PartnerModuleMapper partnerModuleMapper;

    @Mock
    private PartnerService partnerService;

    @Mock
    private RedisPublish redisPublish;

    @Mock
    private BaseJsonUtils jsonUtils;

    @Mock
    private PartnerPointConfigService partnerPointConfigService;

    @InjectMocks
    private PartnerModuleServiceImpl partnerModuleService;

    private PartnerModule partnerModule;
    private PartnerModuleCreateDto createDto;
    private PartnerModuleUpdateDto updateDto;
    private Partner partner;
    private JsonNode configNode;
    private LinkAccountConfig linkAccountConfig;
    private SwapPointConfig swapPointConfig;
    private TopUpPointConfig topUpPointConfig;
    private SpendPointConfig spendPointConfig;
    private PartnerPointConfig partnerPointConfig;

    @BeforeEach
    void setUp() {
        // Setup test data
        partner = new Partner();
        partner.setId(1L);
        partner.setCode("TEST_PARTNER");
        partner.setActive(true);

        partnerModule = new PartnerModule();
        partnerModule.setId(1L);
        partnerModule.setPartnerId(1L);
        partnerModule.setModule(ModuleType.LINK_ACCOUNT);
        partnerModule.setEnabled(true);

        // Create LinkAccountConfig
        linkAccountConfig = new LinkAccountConfig();
        Map<String, TermAndCondition> termsMap = new HashMap<>();
        termsMap.put("en", new TermAndCondition("English Terms", "Content"));
        linkAccountConfig.setTermsAndConditions(termsMap);
        linkAccountConfig.setAllowUnlink(true);

        Map<String, TermAndCondition> unlinkTermsMap = new HashMap<>();
        unlinkTermsMap.put("en", new TermAndCondition("Unlink Terms", "Content"));
        linkAccountConfig.setUnLinkTermsAndConditions(unlinkTermsMap);

        // Create SwapPointConfig
        swapPointConfig = new SwapPointConfig();
        swapPointConfig.setPartnerToVclubEnabled(true);
        swapPointConfig.setVclubToPartnerEnabled(true);
        swapPointConfig.setLimitEnabled(true);
        swapPointConfig.setMinAmount(100L);
        swapPointConfig.setMaxAmount(10000L);

        PointConversionRule conversionRule = new PointConversionRule();
        conversionRule.setType(ConversionType.FIXED);
        conversionRule.setToPartnerRate(BigDecimal.valueOf(2.0));
        conversionRule.setToVinClubRate(BigDecimal.valueOf(0.5));
        swapPointConfig.setConversionRule(conversionRule);

        // Create SpendPointConfig
        spendPointConfig = new SpendPointConfig();
        spendPointConfig.setPartnerEnabled(true);
        spendPointConfig.setVclubEnabled(true);
        spendPointConfig.setAllowPartnerPointCodes(Set.of("POINT_CODE_1", "POINT_CODE_2"));

        // Configure partner spend point settings using inherited fields from PointLimitConfig
        Map<String, Long> maxPartnerPointPerTransactionByPointCode = new HashMap<>();
        maxPartnerPointPerTransactionByPointCode.put("POINT_CODE_1", 5000L);
        maxPartnerPointPerTransactionByPointCode.put("POINT_CODE_2", 5000L);
        spendPointConfig.setMaxPartnerPointPerTransactionByPointCode(maxPartnerPointPerTransactionByPointCode);

        Map<String, Long> partnerDailyQuotaByPointCode = new HashMap<>();
        partnerDailyQuotaByPointCode.put("POINT_CODE_1", 10000L);
        partnerDailyQuotaByPointCode.put("POINT_CODE_2", 10000L);
        spendPointConfig.setPartnerDailyQuotaByPointCode(partnerDailyQuotaByPointCode);

        Map<String, Long> partnerMonthlyQuotaByPointCode = new HashMap<>();
        partnerMonthlyQuotaByPointCode.put("POINT_CODE_1", 50000L);
        partnerMonthlyQuotaByPointCode.put("POINT_CODE_2", 50000L);
        spendPointConfig.setPartnerMonthlyQuotaByPointCode(partnerMonthlyQuotaByPointCode);

        // Configure VinClub spend point settings
        spendPointConfig.setMaxVclubPointPerTransaction(5000L);
        spendPointConfig.setVclubDailyQuota(10000L);
        spendPointConfig.setVclubMonthlyQuota(50000L);

        // Create TopUpPointConfig
        topUpPointConfig = new TopUpPointConfig();
        topUpPointConfig.setPartnerEnabled(true);
        topUpPointConfig.setVclubEnabled(true);
        topUpPointConfig.setAllowPartnerPointCodes(Set.of("POINT_CODE_1", "POINT_CODE_2"));

        // Configure partner top up point settings using inherited fields from PointLimitConfig
        Map<String, Long> topUpMaxPartnerPointPerTransactionByPointCode = new HashMap<>();
        topUpMaxPartnerPointPerTransactionByPointCode.put("POINT_CODE_1", 5000L);
        topUpMaxPartnerPointPerTransactionByPointCode.put("POINT_CODE_2", 5000L);
        topUpPointConfig.setMaxPartnerPointPerTransactionByPointCode(topUpMaxPartnerPointPerTransactionByPointCode);

        Map<String, Long> topUpPartnerDailyQuotaByPointCode = new HashMap<>();
        topUpPartnerDailyQuotaByPointCode.put("POINT_CODE_1", 10000L);
        topUpPartnerDailyQuotaByPointCode.put("POINT_CODE_2", 10000L);
        topUpPointConfig.setPartnerDailyQuotaByPointCode(topUpPartnerDailyQuotaByPointCode);

        Map<String, Long> topUpPartnerMonthlyQuotaByPointCode = new HashMap<>();
        topUpPartnerMonthlyQuotaByPointCode.put("POINT_CODE_1", 50000L);
        topUpPartnerMonthlyQuotaByPointCode.put("POINT_CODE_2", 50000L);
        topUpPointConfig.setPartnerMonthlyQuotaByPointCode(topUpPartnerMonthlyQuotaByPointCode);

        // Configure VinClub top up point settings
        topUpPointConfig.setMaxVclubPointPerTransaction(5000L);
        topUpPointConfig.setVclubDailyQuota(10000L);
        topUpPointConfig.setVclubMonthlyQuota(50000L);

        // Mock JsonNode for configs
        configNode = mock(JsonNode.class);
        partnerModule.setConfig(configNode);

        // Setup PartnerPointConfig
        partnerPointConfig = new PartnerPointConfig();
        partnerPointConfig.setId(1L);
        partnerPointConfig.setPartnerId(1L);
        partnerPointConfig.setCode("POINT_CODE_1");
        partnerPointConfig.setStatus(PartnerStatus.ACTIVE);
        partnerPointConfig.setActive(true);

        // Setup DTOs
        createDto = new PartnerModuleCreateDto();
        createDto.setPartnerId(1L);
        createDto.setModule(ModuleType.LINK_ACCOUNT);
        createDto.setConfig(configNode);

        updateDto = new PartnerModuleUpdateDto();
        updateDto.setConfig(configNode);
        updateDto.setEnabled(true);
    }

    @Test
    void testCreate_Success() {
        // Arrange
        when(partnerService.findById(anyLong())).thenReturn(partner);
        when(partnerModuleRepository.findByPartnerIdAndModule(anyLong(), any(ModuleType.class)))
                .thenReturn(Optional.empty());
        when(partnerModuleMapper.toEntity(any(PartnerModuleCreateDto.class))).thenReturn(partnerModule);
        when(jsonUtils.treeToValue(any(JsonNode.class), eq(LinkAccountConfig.class))).thenReturn(linkAccountConfig);
        when(partnerModuleRepository.save(any(PartnerModule.class))).thenReturn(partnerModule);

        // Act
        PartnerModule result = partnerModuleService.create(createDto);

        // Assert
        assertNotNull(result);
        assertEquals(partnerModule.getId(), result.getId());
        assertEquals(partnerModule.getPartnerId(), result.getPartnerId());
        assertEquals(partnerModule.getModule(), result.getModule());
        assertTrue(result.isEnabled());

        verify(partnerService).findById(createDto.getPartnerId());
        verify(partnerModuleRepository).findByPartnerIdAndModule(createDto.getPartnerId(), createDto.getModule());
        verify(partnerModuleMapper).toEntity(createDto);
        verify(partnerModuleRepository).save(partnerModule);
        verify(redisPublish).sendChange(anyString(), eq(partnerModule.getId()));
    }

    @Test
    void testCreate_ModuleAlreadyExists() {
        // Arrange
        when(partnerService.findById(anyLong())).thenReturn(partner);
        when(partnerModuleRepository.findByPartnerIdAndModule(anyLong(), any(ModuleType.class)))
                .thenReturn(Optional.of(partnerModule));

        // Act & Assert
        BusinessLogicException exception = assertThrows(BusinessLogicException.class, () -> {
            partnerModuleService.create(createDto);
        });

        assertEquals(AppErrorCode.OBJECT_EXISTED.getCode(), exception.getPayload().getCode());

        verify(partnerService).findById(createDto.getPartnerId());
        verify(partnerModuleRepository).findByPartnerIdAndModule(createDto.getPartnerId(), createDto.getModule());
        verify(partnerModuleMapper, never()).toEntity(any(PartnerModuleCreateDto.class));
        verify(partnerModuleRepository, never()).save(any(PartnerModule.class));
    }

    @Test
    void testUpdate_Success() {
        // Arrange
        when(partnerService.findById(anyLong())).thenReturn(partner);
        when(partnerModuleRepository.findById(anyLong())).thenReturn(Optional.of(partnerModule));
        when(jsonUtils.treeToValue(any(JsonNode.class), eq(LinkAccountConfig.class))).thenReturn(linkAccountConfig);
        when(partnerModuleRepository.save(any(PartnerModule.class))).thenReturn(partnerModule);

        // Act
        PartnerModule result = partnerModuleService.update(partner.getId(), 1L, updateDto);

        // Assert
        assertNotNull(result);
        assertEquals(partnerModule.getId(), result.getId());

        verify(partnerService).findById(partner.getId());
        verify(partnerModuleRepository).findById(1L);
        verify(partnerModuleMapper).partialUpdate(updateDto, partnerModule);
        verify(partnerModuleRepository).save(partnerModule);
        verify(redisPublish).sendChange(anyString(), eq(partnerModule.getId()));
    }

    @Test
    void testUpdate_ModuleNotFound() {
        // Arrange
        when(partnerService.findById(anyLong())).thenReturn(partner);
        when(partnerModuleRepository.findById(anyLong())).thenReturn(Optional.empty());

        // Act & Assert
        BusinessLogicException exception = assertThrows(BusinessLogicException.class, () -> {
            partnerModuleService.update(partner.getId(), 1L, updateDto);
        });

        assertEquals(AppErrorCode.NOT_FOUND.getCode(), exception.getPayload().getCode());

        verify(partnerService).findById(partner.getId());
        verify(partnerModuleRepository).findById(1L);
        verify(partnerModuleMapper, never()).partialUpdate(any(PartnerModuleUpdateDto.class), any(PartnerModule.class));
        verify(partnerModuleRepository, never()).save(any(PartnerModule.class));
    }

    @Test
    void testDelete_Success() {
        // Arrange
        when(partnerService.findById(anyLong())).thenReturn(partner);
        when(partnerModuleRepository.findById(anyLong())).thenReturn(Optional.of(partnerModule));
        when(partnerModuleRepository.save(any(PartnerModule.class))).thenReturn(partnerModule);

        // Act
        partnerModuleService.delete(partner.getId(), 1L);

        // Assert
        verify(partnerService).findById(partner.getId());
        verify(partnerModuleRepository).findById(1L);
        verify(partnerModuleRepository).save(partnerModule);
        verify(redisPublish).sendChange(anyString(), eq(partnerModule.getId()));
        assertFalse(partnerModule.isEnabled());
    }

    @Test
    void testFindById_Success() {
        // Arrange
        when(partnerModuleRepository.findById(anyLong())).thenReturn(Optional.of(partnerModule));

        // Act
        PartnerModule result = partnerModuleService.findById(1L);

        // Assert
        assertNotNull(result);
        assertEquals(partnerModule.getId(), result.getId());
        assertEquals(partnerModule.getPartnerId(), result.getPartnerId());
        assertEquals(partnerModule.getModule(), result.getModule());

        verify(partnerModuleRepository).findById(1L);
    }

    @Test
    void testFindById_NotFound() {
        // Arrange
        when(partnerModuleRepository.findById(anyLong())).thenReturn(Optional.empty());

        // Act & Assert
        BusinessLogicException exception = assertThrows(BusinessLogicException.class, () -> {
            partnerModuleService.findById(1L);
        });

        assertEquals(AppErrorCode.NOT_FOUND.getCode(), exception.getPayload().getCode());

        verify(partnerModuleRepository).findById(1L);
    }

    @Test
    void testOptByPartnerAndModule_Found() {
        // Arrange
        when(partnerModuleRepository.findByPartnerIdAndModule(anyLong(), any(ModuleType.class)))
                .thenReturn(Optional.of(partnerModule));

        // Act
        Optional<PartnerModule> result = partnerModuleService.optByPartnerAndModule(1L, ModuleType.LINK_ACCOUNT);

        // Assert
        assertTrue(result.isPresent());
        assertEquals(partnerModule.getId(), result.get().getId());

        verify(partnerModuleRepository).findByPartnerIdAndModule(1L, ModuleType.LINK_ACCOUNT);
    }

    @Test
    void testOptByPartnerAndModule_NotFound() {
        // Arrange
        when(partnerModuleRepository.findByPartnerIdAndModule(anyLong(), any(ModuleType.class)))
                .thenReturn(Optional.empty());

        // Act
        Optional<PartnerModule> result = partnerModuleService.optByPartnerAndModule(1L, ModuleType.LINK_ACCOUNT);

        // Assert
        assertFalse(result.isPresent());

        verify(partnerModuleRepository).findByPartnerIdAndModule(1L, ModuleType.LINK_ACCOUNT);
    }

    @Test
    void testFindByPartnerAndModule_Success() {
        // Arrange
        when(partnerModuleRepository.findByPartnerIdAndModule(anyLong(), any(ModuleType.class)))
                .thenReturn(Optional.of(partnerModule));

        // Act
        PartnerModule result = partnerModuleService.findByPartnerAndModule(1L, ModuleType.LINK_ACCOUNT);

        // Assert
        assertNotNull(result);
        assertEquals(partnerModule.getId(), result.getId());

        verify(partnerModuleRepository).findByPartnerIdAndModule(1L, ModuleType.LINK_ACCOUNT);
    }

    @Test
    void testFindByPartnerAndModule_NotFound() {
        // Arrange
        when(partnerModuleRepository.findByPartnerIdAndModule(anyLong(), any(ModuleType.class)))
                .thenReturn(Optional.empty());

        // Act & Assert
        BusinessLogicException exception = assertThrows(BusinessLogicException.class, () -> {
            partnerModuleService.findByPartnerAndModule(1L, ModuleType.LINK_ACCOUNT);
        });

        assertEquals(AppErrorCode.NOT_FOUND.getCode(), exception.getPayload().getCode());

        verify(partnerModuleRepository).findByPartnerIdAndModule(1L, ModuleType.LINK_ACCOUNT);
    }

    @Test
    void testFindEnabledByModule() {
        // Arrange
        List<PartnerModule> modules = Arrays.asList(partnerModule);
        when(partnerModuleRepository.findByModuleAndEnabledIsTrue(any(ModuleType.class))).thenReturn(modules);

        // Act
        List<PartnerModule> result = partnerModuleService.findEnabledByModule(ModuleType.LINK_ACCOUNT);

        // Assert
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(partnerModule.getId(), result.get(0).getId());

        verify(partnerModuleRepository).findByModuleAndEnabledIsTrue(ModuleType.LINK_ACCOUNT);
    }

    @Test
    void testFilter() {
        // Arrange
        List<PartnerModule> modules = Arrays.asList(partnerModule);
        Page<PartnerModule> page = new PageImpl<>(modules);
        PartnerModuleFilterDto filter = new PartnerModuleFilterDto();
        Pageable pageable = Pageable.unpaged();

        when(partnerModuleRepository.findAll(any(Specification.class), eq(pageable))).thenReturn(page);

        // Act
        Page<PartnerModule> result = partnerModuleService.filter(filter, pageable);

        // Assert
        assertNotNull(result);
        assertEquals(1, result.getTotalElements());
        assertEquals(partnerModule.getId(), result.getContent().get(0).getId());

        verify(partnerModuleRepository).findAll(any(Specification.class), eq(pageable));
    }

    @Test
    void testValidateLinkAccountConfig_Success() {
        // Arrange
        partnerModule.setModule(ModuleType.LINK_ACCOUNT);
        when(jsonUtils.treeToValue(any(JsonNode.class), eq(LinkAccountConfig.class))).thenReturn(linkAccountConfig);
        when(jsonUtils.valueToTree(any(LinkAccountConfig.class))).thenReturn(configNode);
        when(partnerService.findById(anyLong())).thenReturn(partner);
        when(partnerModuleRepository.findByPartnerIdAndModule(anyLong(), any(ModuleType.class)))
                .thenReturn(Optional.empty());
        when(partnerModuleMapper.toEntity(any(PartnerModuleCreateDto.class))).thenReturn(partnerModule);
        when(partnerModuleRepository.save(any(PartnerModule.class))).thenReturn(partnerModule);

        // Act
        partnerModuleService.create(createDto);

        // Assert
        verify(jsonUtils).treeToValue(any(JsonNode.class), eq(LinkAccountConfig.class));
        verify(jsonUtils).valueToTree(linkAccountConfig);
    }

    @Test
    void testValidateSwapPointConfig_Success() {
        // Arrange
        partnerModule.setModule(ModuleType.SWAP_POINT);
        createDto.setModule(ModuleType.SWAP_POINT);
        when(jsonUtils.treeToValue(any(JsonNode.class), eq(SwapPointConfig.class))).thenReturn(swapPointConfig);
        when(partnerService.findById(anyLong())).thenReturn(partner);
        when(partnerModuleRepository.findByPartnerIdAndModule(anyLong(), any(ModuleType.class)))
                .thenReturn(Optional.empty());
        when(partnerModuleMapper.toEntity(any(PartnerModuleCreateDto.class))).thenReturn(partnerModule);
        when(partnerModuleRepository.save(any(PartnerModule.class))).thenReturn(partnerModule);

        // Act
        PartnerModule result = partnerModuleService.create(createDto);

        // Assert
        assertNotNull(result);
        verify(jsonUtils).treeToValue(any(JsonNode.class), eq(SwapPointConfig.class));
    }

    @Test
    void testValidateTopUpPointConfig_Success() {
        // Arrange
        partnerModule.setModule(ModuleType.TOPUP_POINT);
        createDto.setModule(ModuleType.TOPUP_POINT);
        when(jsonUtils.treeToValue(any(JsonNode.class), eq(TopUpPointConfig.class))).thenReturn(topUpPointConfig);
        when(partnerService.findById(anyLong())).thenReturn(partner);
        when(partnerModuleRepository.findByPartnerIdAndModule(anyLong(), any(ModuleType.class)))
                .thenReturn(Optional.empty());
        when(partnerModuleMapper.toEntity(any(PartnerModuleCreateDto.class))).thenReturn(partnerModule);
        when(partnerModuleRepository.save(any(PartnerModule.class))).thenReturn(partnerModule);
        when(partnerPointConfigService.findByCode(anyLong(), anyString())).thenReturn(partnerPointConfig);

        // Act
        PartnerModule result = partnerModuleService.create(createDto);

        // Assert
        assertNotNull(result);
        verify(jsonUtils).treeToValue(any(JsonNode.class), eq(TopUpPointConfig.class));
        verify(partnerPointConfigService, times(2)).findByCode(eq(1L), anyString());
        verify(partnerService).addTag(eq(1L), anySet());
    }

    @Test
    void testValidateTopUpPointConfig_MissingRequiredFields() {
        // Arrange
        partnerModule.setModule(ModuleType.TOPUP_POINT);
        createDto.setModule(ModuleType.TOPUP_POINT);

        // Create invalid config with missing required fields
        TopUpPointConfig invalidConfig = new TopUpPointConfig();
        invalidConfig.setPartnerEnabled(true);
        invalidConfig.setVclubEnabled(false);
        // Missing allowPartnerPointCodes

        when(jsonUtils.treeToValue(any(JsonNode.class), eq(TopUpPointConfig.class))).thenReturn(invalidConfig);
        when(partnerService.findById(anyLong())).thenReturn(partner);
        when(partnerModuleRepository.findByPartnerIdAndModule(anyLong(), any(ModuleType.class)))
                .thenReturn(Optional.empty());
        when(partnerModuleMapper.toEntity(any(PartnerModuleCreateDto.class))).thenReturn(partnerModule);

        // Act & Assert
        BusinessLogicException exception = assertThrows(BusinessLogicException.class, () -> {
            partnerModuleService.create(createDto);
        });

        assertEquals(AppErrorCode.PARAM_REQUIRED.getCode(), exception.getPayload().getCode());
        verify(jsonUtils).treeToValue(any(JsonNode.class), eq(TopUpPointConfig.class));
    }

    @Test
    void testValidateSpendPointConfig_Success() {
        // Arrange
        partnerModule.setModule(ModuleType.SPEND_POINT);
        createDto.setModule(ModuleType.SPEND_POINT);
        when(jsonUtils.treeToValue(any(JsonNode.class), eq(SpendPointConfig.class))).thenReturn(spendPointConfig);
        when(partnerService.findById(anyLong())).thenReturn(partner);
        when(partnerModuleRepository.findByPartnerIdAndModule(anyLong(), any(ModuleType.class)))
                .thenReturn(Optional.empty());
        when(partnerModuleMapper.toEntity(any(PartnerModuleCreateDto.class))).thenReturn(partnerModule);
        when(partnerModuleRepository.save(any(PartnerModule.class))).thenReturn(partnerModule);
        when(partnerPointConfigService.findByCode(anyLong(), anyString())).thenReturn(partnerPointConfig);

        // Act
        PartnerModule result = partnerModuleService.create(createDto);

        // Assert
        assertNotNull(result);
        verify(jsonUtils).treeToValue(any(JsonNode.class), eq(SpendPointConfig.class));
        verify(partnerPointConfigService, times(2)).findByCode(eq(1L), anyString());
        verify(partnerService).addTag(eq(1L), anySet());
    }

    @Test
    void testValidateSpendPointConfig_MissingRequiredFields() {
        // Arrange
        partnerModule.setModule(ModuleType.SPEND_POINT);
        createDto.setModule(ModuleType.SPEND_POINT);

        // Create invalid config with missing required fields
        SpendPointConfig invalidConfig = new SpendPointConfig();
        invalidConfig.setPartnerEnabled(true);
        invalidConfig.setVclubEnabled(false);
        // Missing allowPartnerPointCodes

        when(jsonUtils.treeToValue(any(JsonNode.class), eq(SpendPointConfig.class))).thenReturn(invalidConfig);
        when(partnerService.findById(anyLong())).thenReturn(partner);
        when(partnerModuleRepository.findByPartnerIdAndModule(anyLong(), any(ModuleType.class)))
                .thenReturn(Optional.empty());
        when(partnerModuleMapper.toEntity(any(PartnerModuleCreateDto.class))).thenReturn(partnerModule);

        // Act & Assert
        BusinessLogicException exception = assertThrows(BusinessLogicException.class, () -> {
            partnerModuleService.create(createDto);
        });

        assertEquals(AppErrorCode.PARAM_REQUIRED.getCode(), exception.getPayload().getCode());
        verify(jsonUtils).treeToValue(any(JsonNode.class), eq(SpendPointConfig.class));
    }

    @Test
    void testInvalidateCache_Success() {
        // Arrange
        Long moduleId = 1L;

        // Act
        partnerModuleService.invalidateCache(moduleId);

        // Assert - No exception should be thrown
        // The method should complete successfully
        // Cache invalidation is internal behavior that doesn't need explicit verification
    }

    @Test
    void testValidateTopUpPointConfig_WithInactivePointCode() {
        // Arrange
        partnerModule.setModule(ModuleType.TOPUP_POINT);
        createDto.setModule(ModuleType.TOPUP_POINT);

        PartnerPointConfig inactivePointConfig = new PartnerPointConfig();
        inactivePointConfig.setStatus(PartnerStatus.INACTIVE);

        when(jsonUtils.treeToValue(any(JsonNode.class), eq(TopUpPointConfig.class))).thenReturn(topUpPointConfig);
        when(partnerService.findById(anyLong())).thenReturn(partner);
        when(partnerModuleRepository.findByPartnerIdAndModule(anyLong(), any(ModuleType.class)))
                .thenReturn(Optional.empty());
        when(partnerModuleMapper.toEntity(any(PartnerModuleCreateDto.class))).thenReturn(partnerModule);
        when(partnerPointConfigService.findByCode(anyLong(), anyString())).thenReturn(inactivePointConfig);

        // Act & Assert
        BusinessLogicException exception = assertThrows(BusinessLogicException.class, () -> {
            partnerModuleService.create(createDto);
        });

        assertEquals(AppErrorCode.INVALID_PARAM.getCode(), exception.getPayload().getCode());
        verify(partnerPointConfigService).findByCode(eq(1L), anyString());
    }

    @Test
    void testValidateSpendPointConfig_WithInactivePointCode() {
        // Arrange
        partnerModule.setModule(ModuleType.SPEND_POINT);
        createDto.setModule(ModuleType.SPEND_POINT);

        PartnerPointConfig inactivePointConfig = new PartnerPointConfig();
        inactivePointConfig.setStatus(PartnerStatus.INACTIVE);

        when(jsonUtils.treeToValue(any(JsonNode.class), eq(SpendPointConfig.class))).thenReturn(spendPointConfig);
        when(partnerService.findById(anyLong())).thenReturn(partner);
        when(partnerModuleRepository.findByPartnerIdAndModule(anyLong(), any(ModuleType.class)))
                .thenReturn(Optional.empty());
        when(partnerModuleMapper.toEntity(any(PartnerModuleCreateDto.class))).thenReturn(partnerModule);
        when(partnerPointConfigService.findByCode(anyLong(), anyString())).thenReturn(inactivePointConfig);

        // Act & Assert
        BusinessLogicException exception = assertThrows(BusinessLogicException.class, () -> {
            partnerModuleService.create(createDto);
        });

        assertEquals(AppErrorCode.INVALID_PARAM.getCode(), exception.getPayload().getCode());
        verify(partnerPointConfigService).findByCode(eq(1L), anyString());
    }

    @Test
    void testValidateTopUpPointConfig_MissingAllowPartnerPointCodes() {
        // Arrange
        partnerModule.setModule(ModuleType.TOPUP_POINT);
        createDto.setModule(ModuleType.TOPUP_POINT);

        TopUpPointConfig invalidConfig = new TopUpPointConfig();
        invalidConfig.setPartnerEnabled(true);
        invalidConfig.setVclubEnabled(false);
        // Missing allowPartnerPointCodes

        when(jsonUtils.treeToValue(any(JsonNode.class), eq(TopUpPointConfig.class))).thenReturn(invalidConfig);
        when(partnerService.findById(anyLong())).thenReturn(partner);
        when(partnerModuleRepository.findByPartnerIdAndModule(anyLong(), any(ModuleType.class)))
                .thenReturn(Optional.empty());
        when(partnerModuleMapper.toEntity(any(PartnerModuleCreateDto.class))).thenReturn(partnerModule);

        // Act & Assert
        BusinessLogicException exception = assertThrows(BusinessLogicException.class, () -> {
            partnerModuleService.create(createDto);
        });

        assertEquals(AppErrorCode.PARAM_REQUIRED.getCode(), exception.getPayload().getCode());
    }

    @Test
    void testValidateSpendPointConfig_MissingAllowPartnerPointCodes() {
        // Arrange
        partnerModule.setModule(ModuleType.SPEND_POINT);
        createDto.setModule(ModuleType.SPEND_POINT);

        SpendPointConfig invalidConfig = new SpendPointConfig();
        invalidConfig.setPartnerEnabled(true);
        invalidConfig.setVclubEnabled(false);
        // Missing allowPartnerPointCodes

        when(jsonUtils.treeToValue(any(JsonNode.class), eq(SpendPointConfig.class))).thenReturn(invalidConfig);
        when(partnerService.findById(anyLong())).thenReturn(partner);
        when(partnerModuleRepository.findByPartnerIdAndModule(anyLong(), any(ModuleType.class)))
                .thenReturn(Optional.empty());
        when(partnerModuleMapper.toEntity(any(PartnerModuleCreateDto.class))).thenReturn(partnerModule);

        // Act & Assert
        BusinessLogicException exception = assertThrows(BusinessLogicException.class, () -> {
            partnerModuleService.create(createDto);
        });

        assertEquals(AppErrorCode.PARAM_REQUIRED.getCode(), exception.getPayload().getCode());
    }

    @Test
    void testUpdate_PartnerIdMismatch() {
        // Arrange
        PartnerModule differentPartnerModule = new PartnerModule();
        differentPartnerModule.setId(1L);
        differentPartnerModule.setPartnerId(999L); // Different partner ID
        differentPartnerModule.setModule(ModuleType.LINK_ACCOUNT);
        differentPartnerModule.setEnabled(true);

        when(partnerService.findById(anyLong())).thenReturn(partner);
        when(partnerModuleRepository.findById(anyLong())).thenReturn(Optional.of(differentPartnerModule));

        // Act & Assert
        BusinessLogicException exception = assertThrows(BusinessLogicException.class, () -> {
            partnerModuleService.update(partner.getId(), 1L, updateDto);
        });

        assertEquals(AppErrorCode.NOT_FOUND.getCode(), exception.getPayload().getCode());
        verify(partnerService).findById(partner.getId());
        verify(partnerModuleRepository).findById(1L);
        verify(partnerModuleMapper, never()).partialUpdate(any(), any());
    }

    @Test
    void testDelete_PartnerIdMismatch() {
        // Arrange
        PartnerModule differentPartnerModule = new PartnerModule();
        differentPartnerModule.setId(1L);
        differentPartnerModule.setPartnerId(999L); // Different partner ID
        differentPartnerModule.setModule(ModuleType.LINK_ACCOUNT);
        differentPartnerModule.setEnabled(true);

        when(partnerService.findById(anyLong())).thenReturn(partner);
        when(partnerModuleRepository.findById(anyLong())).thenReturn(Optional.of(differentPartnerModule));

        // Act & Assert
        BusinessLogicException exception = assertThrows(BusinessLogicException.class, () -> {
            partnerModuleService.delete(partner.getId(), 1L);
        });

        assertEquals(AppErrorCode.NOT_FOUND.getCode(), exception.getPayload().getCode());
        verify(partnerService).findById(partner.getId());
        verify(partnerModuleRepository).findById(1L);
        verify(partnerModuleRepository, never()).save(any());
    }
}
