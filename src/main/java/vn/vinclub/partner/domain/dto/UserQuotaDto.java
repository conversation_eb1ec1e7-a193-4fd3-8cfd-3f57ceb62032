package vn.vinclub.partner.domain.dto;

import lombok.Builder;
import lombok.Data;
import vn.vinclub.partner.domain.enums.TransactionType;

import java.math.BigDecimal;
import java.util.Map;

@Builder
@Data
public class UserQuotaDto {
    private Long totalTransaction;
    private BigDecimal totalCashAmount;

    private Map<TransactionType, Map<String, Long>> totalPartnerPointTransactionByTypeAndPointCode;
    private Map<TransactionType, Map<String, Long>> totalPartnerPointAmountByTypeAndPointCode;

    private Map<TransactionType, Long> totalVinclubPointTransactionByType;
    private Map<TransactionType, Long> totalVinclubPointAmountByType;
}
